'use client';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { PlusIconSvg, EditIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';

export default function PhoneNumber() {

    const phoneNumber = "";
    const maskedPhoneNumber = phoneNumber
        ? "*".repeat(phoneNumber.length - 2) + phoneNumber.slice(-2)
        : null;
    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Phone Number</h6>
                            <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href="/account/phone/setup" prefetch={true}>
                                <button className="d-flex align-items-center">
                                    {phoneNumber ? <EditIconSvg /> : <PlusIconSvg />}
                                    <span className="ms-2">{phoneNumber ? 'Update' : 'Add Phone Number'}</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Phone Number </span>{" "}
                                    </Col>
                                    <Col xs={12} md={9}>
                                        <span>{maskedPhoneNumber || "Not set"}</span>
                                    </Col>

                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col>
        </>
    )
}
