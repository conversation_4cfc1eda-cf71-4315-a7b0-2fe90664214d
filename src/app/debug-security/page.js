'use client';
import { useEffect, useState } from 'react';

export default function DebugSecurity() {
    const [cookies, setCookies] = useState('');
    const [testResult, setTestResult] = useState('');

    useEffect(() => {
        // Get all cookies
        setCookies(document.cookie);
    }, []);

    const testSecurityCookie = () => {
        const cookieValue = getCookie('security_verified');
        console.log('Security cookie value:', cookieValue);
        
        if (!cookieValue) {
            setTestResult('No security_verified cookie found');
            return;
        }

        // Test the same validation logic as middleware
        try {
            if (cookieValue === 'true') {
                setTestResult('Cookie is simple true value - VALID');
                return;
            }

            const payload = JSON.parse(atob(cookieValue));
            console.log('Decoded payload:', payload);
            
            if (payload.verified_at) {
                const verifiedAt = new Date(payload.verified_at);
                const now = new Date();
                const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
                
                const isValid = diffInSeconds <= 600;
                setTestResult(`Cookie payload decoded - Time diff: ${diffInSeconds}s - Valid: ${isValid}`);
            } else {
                setTestResult('Cookie payload missing verified_at field');
            }
        } catch (e) {
            setTestResult(`Cookie validation error: ${e.message}`);
        }
    };

    const getCookie = (name) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    };

    const clearSecurityCookie = () => {
        document.cookie = 'security_verified=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        setCookies(document.cookie);
        setTestResult('Security cookie cleared');
    };

    return (
        <div style={{ padding: '20px', fontFamily: 'monospace' }}>
            <h1>Security Cookie Debug</h1>
            
            <div style={{ marginBottom: '20px' }}>
                <h3>All Cookies:</h3>
                <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
                    {cookies || 'No cookies found'}
                </pre>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <button onClick={testSecurityCookie} style={{ marginRight: '10px', padding: '10px' }}>
                    Test Security Cookie
                </button>
                <button onClick={clearSecurityCookie} style={{ padding: '10px' }}>
                    Clear Security Cookie
                </button>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <h3>Test Result:</h3>
                <pre style={{ background: '#f0f0f0', padding: '10px' }}>
                    {testResult || 'Click "Test Security Cookie" to check'}
                </pre>
            </div>

            <div>
                <h3>Test Links:</h3>
                <ul>
                    <li><a href="/account/phone/setup">Go to Phone Setup (should trigger security check)</a></li>
                    <li><a href="/security-check?next=%2Faccount%2Fphone%2Fsetup">Go to Security Check directly</a></li>
                </ul>
            </div>
        </div>
    );
}
