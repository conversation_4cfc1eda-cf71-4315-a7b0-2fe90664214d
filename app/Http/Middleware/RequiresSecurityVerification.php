<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RequiresSecurityVerification
{
    /**
     * Secure pages that require security verification
     */
    protected $securePages = [
        '/account/phone/setup',
        '/account/email/setup',
        '/account/password/change',
        '/account/2fa/setup',
        '/account/create-username',
        '/account/address/manage',
        '/account/address/setup',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to secure pages
        if (!$this->isSecurePage($request->path())) {
            return $next($request);
        }

        // Check for security verification cookie
        $securityVerified = $request->cookie('security_verified');
        
        if (!$securityVerified || !$this->isValidSecurityCookie($securityVerified)) {
            // Store the intended destination
            $nextUrl = $request->fullUrl();
            
            // Redirect to security check with next parameter
            return redirect()->to('/security-check?next=' . urlencode($nextUrl));
        }

        return $next($request);
    }

    /**
     * Check if the current path is a secure page
     */
    protected function isSecurePage(string $path): bool
    {
        foreach ($this->securePages as $securePage) {
            if (str_starts_with('/' . $path, $securePage)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate the security verification cookie
     */
    protected function isValidSecurityCookie($cookieValue): bool
    {
        if (!$cookieValue) {
            return false;
        }

        // For simple implementation, we'll just check if it's 'true'
        // In production, you might want to use encrypted/signed cookies
        if ($cookieValue === 'true') {
            return true;
        }

        // If using encrypted payload, decrypt and validate timestamp
        try {
            $payload = json_decode(base64_decode($cookieValue), true);
            if (isset($payload['verified_at'])) {
                $verifiedAt = new \DateTime($payload['verified_at']);
                $now = new \DateTime();
                $diff = $now->getTimestamp() - $verifiedAt->getTimestamp();
                
                // Check if within 10-minute window (600 seconds)
                return $diff <= 600;
            }
        } catch (\Exception $e) {
            // Invalid cookie format
            return false;
        }

        return false;
    }
}
