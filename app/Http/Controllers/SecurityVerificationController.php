<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use SendGrid;
use SendGrid\Mail\Mail;

class SecurityVerificationController extends Controller
{
    /**
     * Send security verification code to user's email
     */
    public function sendVerificationCode(Request $request)
    {
        dd($request->all());
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Generate 6-digit verification code
            $verificationCode = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

            // Create cache key for this verification session
            $sessionId = Str::uuid();
            $cacheKey = "security_verification_{$user->id}_{$sessionId}";

            // Store verification data in cache for 15 minutes
            Cache::put($cacheKey, [
                'user_id' => $user->id,
                'email' => $user->email,
                'code' => $verificationCode,
                'created_at' => now(),
                'verified' => false
            ], now()->addMinutes(15));

            // Send verification email
            $this->sendSecurityVerificationEmail($user->email, $verificationCode);

            return response()->json([
                'success' => true,
                'message' => 'Verification code sent to your email',
                'session_id' => $sessionId,
                'masked_email' => $this->maskEmail($user->email)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code'
            ], 500);
        }
    }

    /**
     * Verify the security code and set cookie
     */
    public function verifyCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
            'session_id' => 'required|string|uuid',
            'next' => 'nullable|string'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $cacheKey = "security_verification_{$user->id}_{$request->session_id}";
            $verificationData = Cache::get($cacheKey);

            if (!$verificationData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired or invalid'
                ], 400);
            }

            if ($verificationData['code'] !== $request->code) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid verification code'
                ], 400);
            }

            // Mark as verified
            $verificationData['verified'] = true;
            $verificationData['verified_at'] = now();
            Cache::put($cacheKey, $verificationData, now()->addMinutes(15));

            // Create security verification cookie
            $cookieValue = base64_encode(json_encode([
                'user_id' => $user->id,
                'verified_at' => now()->toISOString(),
                'session_id' => $request->session_id
            ]));

            // Set secure cookie for 10 minutes
            $cookie = Cookie::make(
                'security_verified',
                $cookieValue,
                10, // 10 minutes
                '/',
                null,
                true, // secure
                true, // httpOnly
                false,
                'Lax' // sameSite
            );

            $response = response()->json([
                'success' => true,
                'message' => 'Security verification successful',
                'redirect_url' => $request->next ?: '/account/overview'
            ]);

            return $response->withCookie($cookie);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification failed'
            ], 500);
        }
    }

    /**
     * Resend verification code
     */
    public function resendCode(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|uuid'
        ]);

        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Check rate limiting
            $rateLimitKey = "security_resend_{$user->id}";
            if (Cache::has($rateLimitKey)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please wait before requesting another code'
                ], 429);
            }

            // Set rate limit for 60 seconds
            Cache::put($rateLimitKey, true, now()->addSeconds(60));

            $cacheKey = "security_verification_{$user->id}_{$request->session_id}";
            $verificationData = Cache::get($cacheKey);

            if (!$verificationData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Verification session expired'
                ], 400);
            }

            // Generate new code
            $newCode = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

            // Update cache with new code
            $verificationData['code'] = $newCode;
            $verificationData['created_at'] = now();
            Cache::put($cacheKey, $verificationData, now()->addMinutes(15));

            // Send new verification email
            $this->sendSecurityVerificationEmail($user->email, $newCode);

            return response()->json([
                'success' => true,
                'message' => 'New verification code sent'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to resend code'
            ], 500);
        }
    }

    /**
     * Send security verification email
     */
    private function sendSecurityVerificationEmail($email, $code)
    {
        // Use the same email service as AuthController
        $sendgrid = new SendGrid(env('SENDGRID_API_KEY'), ['verify_ssl' => false]);

        $message = new Mail();
        $message->setFrom('<EMAIL>', 'TradeReply');
        $message->setReplyTo('<EMAIL>', 'Support Team');
        $message->setSubject("Security Verification Code");
        $message->addTo($email);

        // Use the same template as signup verification for now
        $message->setTemplateId(env('SENDGRID_VERIFICATION_TEMPLATE_ID', 'd-123456'));
        $message->addDynamicTemplateData('accountVerificationToken', $code);

        try {
            $sendgrid->send($message);
        } catch (\Exception $e) {
            \Log::error('Failed to send security verification email: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Mask email address for display
     */
    private function maskEmail($email)
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 1) {
            return $email;
        }

        $maskedUsername = substr($username, 0, 1) . str_repeat('*', strlen($username) - 1);
        return $maskedUsername . '@' . $domain;
    }
}
