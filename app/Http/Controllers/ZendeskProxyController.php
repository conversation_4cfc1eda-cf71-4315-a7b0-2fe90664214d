<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ZendeskProxyController extends Controller
{
    public function handle($path, Request $request)
    {
        $zendeskDomain = 'https://tradereply.zendesk.com/api/v2/';
        $token = config('services.zendesk.token');

        $fullUrl = $zendeskDomain . $path;

        $http = Http::withToken($token);

        // Forward query parameters and headers (optional)
        if ($request->isMethod('get')) {
            $response = $http->get($fullUrl, $request->query());
        } elseif ($request->isMethod('post')) {
            $response = $http->post($fullUrl, $request->all());
        } else {
            return response()->json(['error' => 'Unsupported method'], 405);
        }

        return response()->json($response->json(), $response->status());
    }
}

